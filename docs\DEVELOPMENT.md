# ContentFlow 开发指南

## 开发环境设置

### 系统要求
- Node.js 18.0 或更高版本
- PostgreSQL 14.0 或更高版本
- Redis 6.0 或更高版本
- Git

### 环境安装

1. **克隆项目**
```bash
git clone https://github.com/your-username/contentflow.git
cd contentflow
```

2. **安装依赖**
```bash
npm install
```

3. **环境配置**
```bash
# 复制环境变量模板
cp .env.example .env.local

# 编辑环境变量
nano .env.local
```

4. **数据库设置**
```bash
# 生成Prisma客户端
npm run db:generate

# 运行数据库迁移
npm run db:migrate

# 填充种子数据
npm run db:seed
```

5. **启动开发服务器**
```bash
npm run dev
```

访问 http://localhost:3000 查看应用。

## 项目结构

```
contentflow/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── (auth)/         # 认证相关页面
│   │   ├── dashboard/      # 仪表板页面
│   │   ├── api/            # API路由
│   │   └── globals.css     # 全局样式
│   ├── components/         # React组件
│   │   ├── ui/            # 基础UI组件
│   │   ├── forms/         # 表单组件
│   │   ├── charts/        # 图表组件
│   │   └── layout/        # 布局组件
│   ├── lib/               # 工具库
│   │   ├── auth.ts        # 认证配置
│   │   ├── db.ts          # 数据库连接
│   │   ├── utils.ts       # 工具函数
│   │   └── validations.ts # 数据验证
│   ├── hooks/             # React Hooks
│   ├── types/             # TypeScript类型定义
│   └── utils/             # 工具函数
├── prisma/                # 数据库相关
│   ├── schema.prisma      # 数据库模式
│   ├── migrations/        # 数据库迁移
│   └── seed.ts           # 种子数据
├── docs/                  # 项目文档
├── public/                # 静态资源
└── tests/                 # 测试文件
```

## 开发工作流

### 1. 功能开发流程

1. **创建功能分支**
```bash
git checkout -b feature/new-feature-name
```

2. **开发功能**
- 编写代码
- 添加测试
- 更新文档

3. **代码检查**
```bash
# 类型检查
npm run type-check

# 代码格式化
npm run lint

# 运行测试
npm test
```

4. **提交代码**
```bash
git add .
git commit -m "feat: add new feature description"
```

5. **推送并创建PR**
```bash
git push origin feature/new-feature-name
```

### 2. 数据库开发

#### 修改数据库模式
1. 编辑 `prisma/schema.prisma`
2. 生成迁移文件
```bash
npm run db:migrate
```

#### 重置数据库
```bash
# 重置数据库（谨慎使用）
npx prisma migrate reset
```

#### 查看数据库
```bash
# 启动Prisma Studio
npm run db:studio
```

### 3. API开发

#### 创建新的API端点
```typescript
// src/app/api/example/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions)
  
  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
  }

  // API逻辑
  return NextResponse.json({ data: 'example' })
}
```

#### API路由约定
- `GET` - 获取数据
- `POST` - 创建数据
- `PUT` - 更新数据
- `DELETE` - 删除数据
- `PATCH` - 部分更新

### 4. 组件开发

#### 创建新组件
```typescript
// src/components/example/ExampleComponent.tsx
import { FC } from 'react'
import { cn } from '@/lib/utils'

interface ExampleComponentProps {
  className?: string
  children?: React.ReactNode
}

export const ExampleComponent: FC<ExampleComponentProps> = ({
  className,
  children,
}) => {
  return (
    <div className={cn('example-component', className)}>
      {children}
    </div>
  )
}
```

#### 组件规范
- 使用TypeScript定义Props接口
- 使用forwardRef处理ref传递
- 使用cn函数合并className
- 添加displayName用于调试

## 测试指南

### 1. 单元测试
```typescript
// tests/utils.test.ts
import { formatDate } from '@/lib/utils'

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2024-01-01')
    expect(formatDate(date)).toBe('2024/1/1')
  })
})
```

### 2. 组件测试
```typescript
// tests/components/Button.test.tsx
import { render, screen } from '@testing-library/react'
import { Button } from '@/components/ui/button'

describe('Button', () => {
  it('renders button with text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button')).toHaveTextContent('Click me')
  })
})
```

### 3. API测试
```typescript
// tests/api/users.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/users/route'

describe('/api/users', () => {
  it('returns users list', async () => {
    const { req, res } = createMocks({
      method: 'GET',
    })

    await handler(req, res)
    expect(res._getStatusCode()).toBe(200)
  })
})
```

## 代码规范

### 1. TypeScript规范
- 优先使用接口而非类型别名
- 使用严格的类型检查
- 避免使用any类型
- 使用枚举定义常量

### 2. React规范
- 使用函数组件和Hooks
- 组件名使用PascalCase
- Props接口以组件名+Props命名
- 使用memo优化性能

### 3. 样式规范
- 使用Tailwind CSS类名
- 避免内联样式
- 使用CSS变量定义主题
- 响应式设计优先

### 4. 文件命名规范
- 组件文件使用PascalCase
- 工具文件使用camelCase
- 常量文件使用UPPER_CASE
- 测试文件添加.test后缀

## 性能优化

### 1. 前端优化
- 使用React.memo避免不必要的重渲染
- 使用useMemo和useCallback缓存计算结果
- 代码分割和懒加载
- 图片优化和懒加载

### 2. 后端优化
- 数据库查询优化
- 使用Redis缓存
- API响应压缩
- 连接池管理

### 3. 构建优化
- Tree shaking移除未使用代码
- 代码压缩和混淆
- 静态资源CDN
- 服务端渲染(SSR)

## 调试技巧

### 1. 前端调试
```typescript
// 使用React DevTools
// 使用console.log调试
console.log('Debug info:', data)

// 使用debugger断点
debugger

// 使用React Query DevTools
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
```

### 2. 后端调试
```typescript
// API调试
console.log('Request:', request)
console.log('Response:', response)

// 数据库调试
const result = await prisma.user.findMany({
  where: { email: '<EMAIL>' }
})
console.log('Database result:', result)
```

### 3. 网络调试
- 使用浏览器开发者工具
- 使用Postman测试API
- 检查网络请求和响应

## 部署指南

### 1. 本地部署
```bash
# 构建应用
npm run build

# 启动生产服务器
npm start
```

### 2. Docker部署
```bash
# 构建Docker镜像
docker build -t contentflow .

# 运行容器
docker run -p 3000:3000 contentflow
```

### 3. 云平台部署
- **Vercel**: 连接GitHub自动部署
- **Netlify**: 静态站点部署
- **Railway**: 全栈应用部署
- **AWS/GCP**: 云服务器部署

## 常见问题

### 1. 数据库连接问题
```bash
# 检查数据库连接
npx prisma db pull

# 重新生成客户端
npx prisma generate
```

### 2. 依赖安装问题
```bash
# 清除缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install
```

### 3. 类型错误
```bash
# 重新生成类型
npm run type-check

# 检查TypeScript配置
npx tsc --showConfig
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

### Commit消息规范
- `feat:` 新功能
- `fix:` 修复bug
- `docs:` 文档更新
- `style:` 代码格式化
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动
