# 数据库配置
DATABASE_URL="postgresql://username:password@localhost:5432/contentflow"

# NextAuth.js 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# Redis 配置
REDIS_URL="redis://localhost:6379"

# AI 服务配置
OPENAI_API_KEY="your-openai-api-key"
ANTHROPIC_API_KEY="your-anthropic-api-key"

# 社交媒体 API 配置
TWITTER_API_KEY="your-twitter-api-key"
TWITTER_API_SECRET="your-twitter-api-secret"
FACEBOOK_APP_ID="your-facebook-app-id"
FACEBOOK_APP_SECRET="your-facebook-app-secret"
LINKEDIN_CLIENT_ID="your-linkedin-client-id"
LINKEDIN_CLIENT_SECRET="your-linkedin-client-secret"

# SEO 工具 API 配置
GOOGLE_SEARCH_CONSOLE_CLIENT_ID="your-gsc-client-id"
GOOGLE_SEARCH_CONSOLE_CLIENT_SECRET="your-gsc-client-secret"
SEMRUSH_API_KEY="your-semrush-api-key"
AHREFS_API_KEY="your-ahrefs-api-key"

# 邮件服务配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# 文件存储配置
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="contentflow-uploads"

# 应用配置
NODE_ENV="development"
PORT="3000"
APP_URL="http://localhost:3000"

# 监控和分析
SENTRY_DSN="your-sentry-dsn"
GOOGLE_ANALYTICS_ID="your-ga-id"
