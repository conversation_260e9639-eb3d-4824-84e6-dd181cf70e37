{"name": "contentflow", "version": "0.1.0", "description": "开源内容营销自动化平台", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@next/font": "^14.0.0", "@prisma/client": "^5.7.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.8.0", "@tanstack/react-table": "^8.10.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "next": "^14.0.0", "next-auth": "^4.24.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "autoprefixer": "^10.4.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.0", "jest": "^29.7.0", "postcss": "^8.4.0", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.0", "prisma": "^5.7.0", "tailwindcss": "^3.3.0", "tsx": "^4.6.0", "typescript": "^5.2.0"}, "keywords": ["content-marketing", "seo", "social-media", "automation", "nextjs", "typescript", "open-source"], "author": "ContentFlow Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/contentflow.git"}, "bugs": {"url": "https://github.com/your-username/contentflow/issues"}, "homepage": "https://github.com/your-username/contentflow#readme"}