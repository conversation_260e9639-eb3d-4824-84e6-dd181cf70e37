// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  EDITOR = 'EDITOR',
}

// 网站/项目相关类型
export interface Website {
  id: string;
  name: string;
  url: string;
  description?: string;
  industry: Industry;
  userId: string;
  settings: WebsiteSettings;
  createdAt: Date;
  updatedAt: Date;
}

export enum Industry {
  TOOLS = 'TOOLS',
  DESIGN = 'DESIGN',
  EDUCATION = 'EDUCATION',
  ECOMMERCE = 'ECOMMERCE',
  SERVICES = 'SERVICES',
  BLOG = 'BLOG',
  OTHER = 'OTHER',
}

export interface WebsiteSettings {
  seoSettings: SEOSettings;
  socialSettings: SocialSettings;
  contentSettings: ContentSettings;
}

// SEO相关类型
export interface SEOSettings {
  primaryKeywords: string[];
  targetAudience: string;
  competitorUrls: string[];
  googleSearchConsoleConnected: boolean;
}

export interface Keyword {
  id: string;
  keyword: string;
  searchVolume: number;
  difficulty: number;
  currentRank?: number;
  targetRank: number;
  websiteId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface RankingData {
  id: string;
  keywordId: string;
  position: number;
  url: string;
  date: Date;
}

// 内容相关类型
export interface ContentSettings {
  aiProvider: AIProvider;
  contentTone: ContentTone;
  contentLength: ContentLength;
  languages: string[];
}

export enum AIProvider {
  OPENAI = 'OPENAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
}

export enum ContentTone {
  PROFESSIONAL = 'PROFESSIONAL',
  CASUAL = 'CASUAL',
  FRIENDLY = 'FRIENDLY',
  AUTHORITATIVE = 'AUTHORITATIVE',
}

export enum ContentLength {
  SHORT = 'SHORT',
  MEDIUM = 'MEDIUM',
  LONG = 'LONG',
}

export interface ContentPiece {
  id: string;
  title: string;
  content: string;
  type: ContentType;
  status: ContentStatus;
  keywords: string[];
  publishDate?: Date;
  websiteId: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ContentType {
  BLOG_POST = 'BLOG_POST',
  SOCIAL_POST = 'SOCIAL_POST',
  EMAIL = 'EMAIL',
  LANDING_PAGE = 'LANDING_PAGE',
  PRODUCT_DESCRIPTION = 'PRODUCT_DESCRIPTION',
}

export enum ContentStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
}

// 社交媒体相关类型
export interface SocialSettings {
  platforms: SocialPlatform[];
  autoPost: boolean;
  postingSchedule: PostingSchedule;
}

export interface SocialPlatform {
  platform: PlatformType;
  connected: boolean;
  accountId?: string;
  accountName?: string;
}

export enum PlatformType {
  TWITTER = 'TWITTER',
  FACEBOOK = 'FACEBOOK',
  LINKEDIN = 'LINKEDIN',
  INSTAGRAM = 'INSTAGRAM',
  YOUTUBE = 'YOUTUBE',
}

export interface PostingSchedule {
  timezone: string;
  schedule: ScheduleSlot[];
}

export interface ScheduleSlot {
  day: number; // 0-6 (Sunday-Saturday)
  times: string[]; // HH:MM format
}

// 外链相关类型
export interface Backlink {
  id: string;
  sourceUrl: string;
  targetUrl: string;
  anchorText: string;
  status: BacklinkStatus;
  domainAuthority?: number;
  websiteId: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum BacklinkStatus {
  ACTIVE = 'ACTIVE',
  LOST = 'LOST',
  PENDING = 'PENDING',
  REJECTED = 'REJECTED',
}

export interface DirectorySubmission {
  id: string;
  directoryName: string;
  directoryUrl: string;
  submissionUrl?: string;
  status: SubmissionStatus;
  websiteId: string;
  submittedAt?: Date;
  createdAt: Date;
}

export enum SubmissionStatus {
  PENDING = 'PENDING',
  SUBMITTED = 'SUBMITTED',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

// 分析相关类型
export interface Analytics {
  websiteId: string;
  period: AnalyticsPeriod;
  metrics: AnalyticsMetrics;
  date: Date;
}

export enum AnalyticsPeriod {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  QUARTER = 'QUARTER',
  YEAR = 'YEAR',
}

export interface AnalyticsMetrics {
  organicTraffic: number;
  keywordRankings: number;
  backlinks: number;
  socialEngagement: number;
  contentViews: number;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 表单相关类型
export interface CreateWebsiteForm {
  name: string;
  url: string;
  description?: string;
  industry: Industry;
}

export interface UpdateWebsiteForm extends Partial<CreateWebsiteForm> {
  id: string;
}
