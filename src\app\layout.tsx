import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import '@/styles/globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'ContentFlow - 开源内容营销平台',
  description: '现代化的开源内容营销自动化平台，专为小型企业、自由职业者和营销团队设计',
  keywords: ['内容营销', 'SEO', '社交媒体', '自动化', '开源'],
  authors: [{ name: 'ContentFlow Team' }],
  creator: 'ContentFlow Team',
  publisher: 'ContentFlow',
  robots: 'index, follow',
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: 'https://contentflow.dev',
    title: 'ContentFlow - 开源内容营销平台',
    description: '现代化的开源内容营销自动化平台',
    siteName: 'ContentFlow',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'ContentFlow - 开源内容营销平台',
    description: '现代化的开源内容营销自动化平台',
    creator: '@contentflow',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <div className="min-h-screen bg-background">
          {children}
        </div>
      </body>
    </html>
  )
}
