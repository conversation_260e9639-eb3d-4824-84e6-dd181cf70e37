import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  BarChart3, 
  Bot, 
  Globe, 
  Link, 
  Share2, 
  Target,
  Users,
  Zap,
  ArrowRight,
  CheckCircle
} from 'lucide-react'

export default function HomePage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
              <Zap className="h-5 w-5 text-primary-foreground" />
            </div>
            <span className="text-xl font-bold">ContentFlow</span>
          </div>
          <nav className="hidden md:flex items-center space-x-6">
            <a href="#features" className="text-sm font-medium hover:text-primary transition-colors">
              功能特性
            </a>
            <a href="#pricing" className="text-sm font-medium hover:text-primary transition-colors">
              价格方案
            </a>
            <a href="#docs" className="text-sm font-medium hover:text-primary transition-colors">
              文档
            </a>
            <Button variant="outline" size="sm">
              登录
            </Button>
            <Button size="sm">
              开始使用
            </Button>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 text-center bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        <div className="container max-w-4xl">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight mb-6">
            让内容营销变得
            <span className="text-primary"> 简单高效</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            ContentFlow 是一个现代化的开源内容营销自动化平台，集成 AI 内容生成、SEO 优化、社交媒体管理和外链建设于一体。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="text-lg px-8">
              免费开始使用
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8">
              查看演示
            </Button>
          </div>
          <div className="mt-12 flex items-center justify-center space-x-8 text-sm text-muted-foreground">
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              完全开源
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              自托管部署
            </div>
            <div className="flex items-center">
              <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
              无使用限制
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 px-4">
        <div className="container">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">强大的功能特性</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              从内容创作到效果分析，ContentFlow 提供完整的内容营销解决方案
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Bot className="h-12 w-12 text-primary mb-4" />
                <CardTitle>AI 内容生成</CardTitle>
                <CardDescription>
                  智能创建高质量营销内容，支持多种内容类型和风格定制
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Target className="h-12 w-12 text-primary mb-4" />
                <CardTitle>SEO 优化套件</CardTitle>
                <CardDescription>
                  关键词跟踪、排名监控、技术SEO检查和竞争对手分析
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Share2 className="h-12 w-12 text-primary mb-4" />
                <CardTitle>社交媒体管理</CardTitle>
                <CardDescription>
                  多平台发布、智能调度、互动分析和内容自动适配
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Link className="h-12 w-12 text-primary mb-4" />
                <CardTitle>外链建设自动化</CardTitle>
                <CardDescription>
                  自动目录提交、外链监控、客座发文管理和合作伙伴维护
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <BarChart3 className="h-12 w-12 text-primary mb-4" />
                <CardTitle>数据分析报告</CardTitle>
                <CardDescription>
                  实时数据监控、效果分析、趋势预测和可视化报告
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <Users className="h-12 w-12 text-primary mb-4" />
                <CardTitle>多租户管理</CardTitle>
                <CardDescription>
                  多网站管理、团队协作、权限控制和个性化配置
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-primary text-primary-foreground">
        <div className="container text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            准备开始你的内容营销之旅？
          </h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            加入数千家企业，使用 ContentFlow 提升你的内容营销效果
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="text-lg px-8">
              立即开始
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary">
              联系我们
            </Button>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-12 px-4">
        <div className="container">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="h-8 w-8 rounded-lg bg-primary flex items-center justify-center">
                  <Zap className="h-5 w-5 text-primary-foreground" />
                </div>
                <span className="text-xl font-bold">ContentFlow</span>
              </div>
              <p className="text-muted-foreground">
                开源内容营销自动化平台，让营销变得简单高效。
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">产品</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><a href="#" className="hover:text-foreground transition-colors">功能特性</a></li>
                <li><a href="#" className="hover:text-foreground transition-colors">价格方案</a></li>
                <li><a href="#" className="hover:text-foreground transition-colors">更新日志</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">资源</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><a href="#" className="hover:text-foreground transition-colors">文档</a></li>
                <li><a href="#" className="hover:text-foreground transition-colors">API 参考</a></li>
                <li><a href="#" className="hover:text-foreground transition-colors">社区</a></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">公司</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li><a href="#" className="hover:text-foreground transition-colors">关于我们</a></li>
                <li><a href="#" className="hover:text-foreground transition-colors">联系我们</a></li>
                <li><a href="#" className="hover:text-foreground transition-colors">隐私政策</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t mt-8 pt-8 text-center text-muted-foreground">
            <p>&copy; 2024 ContentFlow. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
