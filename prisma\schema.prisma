// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  avatar    String?
  role      UserRole @default(USER)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  websites Website[]
  sessions Session[]
  accounts Account[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model Website {
  id          String   @id @default(cuid())
  name        String
  url         String
  description String?
  industry    Industry
  userId      String
  settings    Json     @default("{}")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user                User                  @relation(fields: [userId], references: [id], onDelete: Cascade)
  keywords            Keyword[]
  contentPieces       ContentPiece[]
  backlinks           Backlink[]
  directorySubmissions DirectorySubmission[]
  analytics           Analytics[]

  @@map("websites")
}

model Keyword {
  id           String   @id @default(cuid())
  keyword      String
  searchVolume Int      @default(0)
  difficulty   Int      @default(0)
  currentRank  Int?
  targetRank   Int
  websiteId    String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  website     Website       @relation(fields: [websiteId], references: [id], onDelete: Cascade)
  rankingData RankingData[]

  @@map("keywords")
}

model RankingData {
  id        String   @id @default(cuid())
  keywordId String
  position  Int
  url       String
  date      DateTime @default(now())

  // Relations
  keyword Keyword @relation(fields: [keywordId], references: [id], onDelete: Cascade)

  @@map("ranking_data")
}

model ContentPiece {
  id          String        @id @default(cuid())
  title       String
  content     String        @db.Text
  type        ContentType
  status      ContentStatus @default(DRAFT)
  keywords    String[]
  publishDate DateTime?
  websiteId   String
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  website Website @relation(fields: [websiteId], references: [id], onDelete: Cascade)

  @@map("content_pieces")
}

model Backlink {
  id              String        @id @default(cuid())
  sourceUrl       String
  targetUrl       String
  anchorText      String
  status          BacklinkStatus @default(PENDING)
  domainAuthority Int?
  websiteId       String
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  website Website @relation(fields: [websiteId], references: [id], onDelete: Cascade)

  @@map("backlinks")
}

model DirectorySubmission {
  id            String           @id @default(cuid())
  directoryName String
  directoryUrl  String
  submissionUrl String?
  status        SubmissionStatus @default(PENDING)
  websiteId     String
  submittedAt   DateTime?
  createdAt     DateTime         @default(now())

  // Relations
  website Website @relation(fields: [websiteId], references: [id], onDelete: Cascade)

  @@map("directory_submissions")
}

model Analytics {
  id        String          @id @default(cuid())
  websiteId String
  period    AnalyticsPeriod
  metrics   Json
  date      DateTime        @default(now())

  // Relations
  website Website @relation(fields: [websiteId], references: [id], onDelete: Cascade)

  @@map("analytics")
}

// Enums
enum UserRole {
  ADMIN
  USER
  EDITOR
}

enum Industry {
  TOOLS
  DESIGN
  EDUCATION
  ECOMMERCE
  SERVICES
  BLOG
  OTHER
}

enum ContentType {
  BLOG_POST
  SOCIAL_POST
  EMAIL
  LANDING_PAGE
  PRODUCT_DESCRIPTION
}

enum ContentStatus {
  DRAFT
  SCHEDULED
  PUBLISHED
  ARCHIVED
}

enum BacklinkStatus {
  ACTIVE
  LOST
  PENDING
  REJECTED
}

enum SubmissionStatus {
  PENDING
  SUBMITTED
  APPROVED
  REJECTED
}

enum AnalyticsPeriod {
  DAY
  WEEK
  MONTH
  QUARTER
  YEAR
}
